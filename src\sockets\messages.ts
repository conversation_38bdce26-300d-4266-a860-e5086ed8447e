import "dotenv/config";
import { Server } from "socket.io";
import http from "http";
import { db } from "@/db";
import { conversation } from "@/db/schema";

const httpServer = http.createServer();
const port = Number(process.env.NEXT_PUBLIC_MESSAGE_SOCKET_PORT);

console.log("Socket.IO server starting on port:", port);
console.log("CORS origin:", process.env.NEXT_PUBLIC_HOST_URL);

const io = new Server(httpServer, {
  cors: {
    origin: process.env.NEXT_PUBLIC_HOST_URL,
    methods: ["GET", "POST"],
    credentials: true,
    allowedHeaders: ["*"],
  },
});

io.on("connection", (socket) => {
  console.log("User connected:", socket.id);

  // Join rooms based on conversation IDs
  socket.on("join", async (conversationId: string) => {
    socket.join(conversationId);
    console.log(`User ${socket.id} joined ${conversationId}`);
  });

  // Send message to group/direct
  socket.on("message", async ({ conversationId, sender, text }) => {
    const newMessage = {
      sender,
      text,
      timestamp: new Date().toISOString(),
    };

    // Save message to DB
    await db.insert(message).values({
      conversationId,
      sender,
      text,
    });

    // Broadcast message to all users in conversation
    io.to(conversationId).emit("message", { conversationId, ...newMessage });
  });

  socket.on("disconnect", () => {
    console.log("User disconnected:", socket.id);
  });
});

httpServer.listen(port, () => {
  console.log(`Socket.IO running on :${port}`);
});
